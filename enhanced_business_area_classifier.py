#!/usr/bin/env python3
# -*- coding: utf-8 -*-
"""
昆山奶茶店商圈分类增强工具
基于昆山城市总体规划和实际地理信息进行精准分类
"""

import pandas as pd
import numpy as np
from kunshan_location_db import get_kunshan_location_database, get_special_keywords, get_road_patterns
import re

def enhanced_classify_business_area():
    """增强版商圈分类"""
    print("=== 昆山奶茶店商圈分类增强工具 ===")
    print("基于昆山城市总体规划进行精准商圈分类...")
    
    # 读取最终数据
    try:
        df = pd.read_excel('昆山奶茶店_最终数据.xlsx')
        print(f"✓ 成功加载数据: {len(df)} 行")
    except FileNotFoundError:
        print("❌ 未找到最终数据文件，请先运行前面的脚本")
        return
    
    # 加载增强的地理数据库
    location_db = get_kunshan_location_database()
    special_keywords = get_special_keywords()
    road_patterns = get_road_patterns()
    
    print("\n=== 当前商圈分布分析 ===")
    current_distribution = df['商圈'].value_counts()
    unknown_count = 0
    for area, count in current_distribution.items():
        if area in ['未知商圈', '其他商圈']:
            print(f"❌ {area}: {count}家")
            unknown_count += count
        else:
            print(f"✅ {area}: {count}家")
    
    print(f"\n需要重新分类的店铺: {unknown_count}家")
    
    if unknown_count == 0:
        print("✅ 所有店铺已正确分类！")
        return
    
    print("\n=== 开始增强分类 ===")
    
    # 创建副本进行修改
    df_enhanced = df.copy()
    enhanced_count = 0
    enhancement_details = []
    
    # 获取需要重新分类的店铺
    need_reclassify = df[df['商圈'].isin(['未知商圈', '其他商圈'])]
    
    for idx, row in need_reclassify.iterrows():
        shop_name = str(row['店铺名称'])
        address = str(row['地址'])
        original_area = row['商圈']
        
        # 使用增强分类算法
        new_area = enhanced_classify_single_shop(shop_name, address, location_db, special_keywords, road_patterns)
        
        if new_area != original_area and new_area not in ['未知商圈', '其他商圈']:
            df_enhanced.at[idx, '商圈'] = new_area
            enhanced_count += 1
            enhancement_details.append({
                'shop_name': shop_name,
                'original': original_area,
                'new': new_area,
                'reason': '基于城市规划增强分类'
            })
            
            if enhanced_count <= 15:  # 显示前15个
                print(f"{enhanced_count:2d}. {shop_name[:25]:<25} {original_area} → {new_area}")
    
    if enhanced_count > 15:
        print(f"    ... 还有 {enhanced_count - 15} 家店铺被重新分类")
    
    # 保存增强后的数据
    output_file = '昆山奶茶店_增强分类数据.xlsx'
    df_enhanced.to_excel(output_file, index=False)
    print(f"✓ 增强分类数据已保存到: {output_file}")
    
    # 生成增强报告
    generate_enhancement_report(df, df_enhanced, enhancement_details)
    
    # 显示最终分布
    print("\n=== 增强分类后商圈分布 ===")
    final_distribution = df_enhanced['商圈'].value_counts()
    final_unknown = 0
    for area, count in final_distribution.items():
        if area in ['未知商圈', '其他商圈']:
            print(f"❌ {area}: {count}家")
            final_unknown += count
        else:
            print(f"✅ {area}: {count}家")
    
    # 计算改善情况
    improvement = unknown_count - final_unknown
    improvement_rate = (improvement / unknown_count * 100) if unknown_count > 0 else 0
    
    print(f"\n✅ 增强分类完成!")
    print(f"✅ 成功重新分类: {enhanced_count}家店铺")
    print(f"✅ 剩余问题商圈: {final_unknown}家")
    print(f"✅ 改善率: {improvement_rate:.1f}%")
    
    print(f"\n下一步操作:")
    print(f"1. 使用增强分类数据文件: {output_file}")
    print(f"2. 导入FineBI进行分析")
    print(f"3. 查看增强报告了解详情")

def enhanced_classify_single_shop(shop_name, address, location_db, special_keywords, road_patterns):
    """增强版单店铺分类算法"""
    
    # 合并所有文本信息
    full_text = f"{shop_name} {address}".lower()
    
    # 1. 优先匹配精确地标
    exact_matches = {
        '万达': '万达商圈',
        '吾悦': '吾悦商圈',
        '花桥': '花桥商圈',
        '千灯': '千灯商圈',
        '张浦': '张浦商圈',
        '巴城': '巴城商圈',
        '周庄': '周庄商圈',
        '锦溪': '锦溪商圈',
        '淀山湖': '淀山湖商圈',
        '陆家': '陆家商圈',
        '周市': '城北',
        '开发区': '昆山开发区',
        '高新区': '昆山高新区',
        '富士康': '昆山开发区'
    }
    
    for keyword, area in exact_matches.items():
        if keyword in full_text:
            return area
    
    # 2. 道路模式匹配
    road_area_mapping = {
        r'人民路|前进路|同心路|柏庐路|亭林路': '中心城区',
        r'绿地大道|花安路|花桥路|商务大道': '花桥商圈',
        r'白塘路|横长泾路|周市': '城北',
        r'石浦路|千灯大道|古镇路': '千灯商圈',
        r'振新路|港浦路|张浦': '张浦商圈',
        r'湖滨路|正仪路|阳澄湖': '巴城商圈',
        r'陆丰路|菉溪路|陆家': '陆家商圈',
        r'萧林路|萧林.*路': '吾悦商圈',
        r'纬.*路|经.*路|富士康': '昆山开发区',
        r'科技路|创新路|高新路': '昆山高新区'
    }
    
    for pattern, area in road_area_mapping.items():
        if re.search(pattern, full_text):
            return area
    
    # 3. 地理数据库匹配（加权评分）
    best_area = None
    best_score = 0
    
    for area, info in location_db.items():
        score = 0
        
        # 主要道路匹配（权重3）
        for road in info['main_roads']:
            if road in full_text:
                score += 3
        
        # 地标匹配（权重2）
        for landmark in info['landmarks']:
            if landmark in full_text:
                score += 2
        
        # 区域名称匹配（权重1）
        for area_name in info['areas']:
            if area_name in full_text:
                score += 1
        
        if score > best_score:
            best_score = score
            best_area = area
    
    # 如果得分足够高，返回该商圈
    if best_score >= 2:
        return best_area
    
    # 4. 基于店铺名称的智能推断
    if any(keyword in shop_name.lower() for keyword in ['花桥', '国际', 'cbd']):
        return '花桥商圈'
    elif any(keyword in shop_name.lower() for keyword in ['万达', 'wanda']):
        return '万达商圈'
    elif any(keyword in shop_name.lower() for keyword in ['吾悦', '新城']):
        return '吾悦商圈'
    elif any(keyword in shop_name.lower() for keyword in ['古镇', '千灯']):
        return '千灯商圈'
    elif any(keyword in shop_name.lower() for keyword in ['周庄']):
        return '周庄商圈'
    elif any(keyword in shop_name.lower() for keyword in ['张浦']):
        return '张浦商圈'
    elif any(keyword in shop_name.lower() for keyword in ['巴城', '阳澄湖']):
        return '巴城商圈'
    elif any(keyword in shop_name.lower() for keyword in ['陆家']):
        return '陆家商圈'
    elif any(keyword in shop_name.lower() for keyword in ['周市', '城北']):
        return '城北'
    elif any(keyword in shop_name.lower() for keyword in ['锦溪']):
        return '锦溪商圈'
    elif any(keyword in shop_name.lower() for keyword in ['淀山湖']):
        return '淀山湖商圈'
    
    # 5. 默认分类到中心城区（如果包含昆山相关词汇）
    if any(keyword in full_text for keyword in ['昆山', '市区', '城区', '中心', '玉山']):
        return '中心城区'
    
    # 6. 无法确定，保持原状
    return '其他商圈'

def generate_enhancement_report(df_original, df_enhanced, enhancement_details):
    """生成增强分类报告"""
    
    report_content = f"""# 昆山奶茶店商圈分类增强报告

## 增强概况
- 增强时间: {pd.Timestamp.now().strftime('%Y-%m-%d %H:%M:%S')}
- 总店铺数: {len(df_original)}
- 成功重新分类: {len(enhancement_details)}家店铺
- 基于: 昆山城市总体规划(2009-2030)和实际地理信息

## 增强前后对比

### 增强前商圈分布
"""
    
    original_dist = df_original['商圈'].value_counts()
    for area, count in original_dist.items():
        report_content += f"- {area}: {count}家\n"
    
    report_content += "\n### 增强后商圈分布\n"
    
    enhanced_dist = df_enhanced['商圈'].value_counts()
    for area, count in enhanced_dist.items():
        report_content += f"- {area}: {count}家\n"
    
    report_content += "\n## 详细增强记录\n\n"
    
    for i, detail in enumerate(enhancement_details, 1):
        report_content += f"{i}. **{detail['shop_name']}**\n"
        report_content += f"   - 原分类: {detail['original']}\n"
        report_content += f"   - 新分类: {detail['new']}\n"
        report_content += f"   - 增强原因: {detail['reason']}\n\n"
    
    # 保存报告
    with open('商圈分类增强报告.md', 'w', encoding='utf-8') as f:
        f.write(report_content)
    
    print("✓ 增强报告已保存到: 商圈分类增强报告.md")

if __name__ == "__main__":
    enhanced_classify_business_area()
